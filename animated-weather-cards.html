<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animated Weather Cards</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1e1e2e, #2d2d44);
            font-family: 'Arial', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            overflow-x: auto;
        }

        .container {
            display: flex;
            gap: 20px;
            padding: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .weather-card {
            width: 280px;
            height: 350px;
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .weather-card:hover {
            transform: translateY(-10px);
        }

        .card-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: rgba(0, 0, 0, 0.4);
            backdrop-filter: blur(10px);
            z-index: 10;
        }

        .weather-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .weather-temp {
            font-size: 18px;
            opacity: 0.8;
        }

        /* Wind Card */
        .wind-card {
            background: linear-gradient(135deg, #4a90e2, #7bb3f0);
        }

        .wind-lines {
            position: absolute;
            width: 100%;
            height: 100%;
        }

        .wind-line {
            position: absolute;
            height: 2px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 1px;
            animation: windMove 3s linear infinite;
        }

        .wind-line:nth-child(1) { top: 20%; width: 60px; animation-delay: 0s; }
        .wind-line:nth-child(2) { top: 35%; width: 80px; animation-delay: 0.5s; }
        .wind-line:nth-child(3) { top: 50%; width: 70px; animation-delay: 1s; }
        .wind-line:nth-child(4) { top: 65%; width: 90px; animation-delay: 1.5s; }

        @keyframes windMove {
            0% { transform: translateX(-100px); opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { transform: translateX(300px); opacity: 0; }
        }

        /* Rain Card */
        .rain-card {
            background: linear-gradient(135deg, #4a5568, #718096);
        }

        .raindrop {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, transparent, #87ceeb);
            border-radius: 0 0 2px 2px;
            animation: rainFall 1s linear infinite;
        }

        @keyframes rainFall {
            0% { transform: translateY(-20px); opacity: 1; }
            100% { transform: translateY(370px); opacity: 0; }
        }

        /* Sun Card */
        .sun-card {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
        }

        .sun {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 80px;
            background: #ffeb3b;
            border-radius: 50%;
            box-shadow: 0 0 30px #ffeb3b;
            animation: sunPulse 2s ease-in-out infinite alternate;
        }

        .sun-ray {
            position: absolute;
            top: 50px;
            left: 50%;
            width: 4px;
            height: 30px;
            background: #ffeb3b;
            border-radius: 2px;
            transform-origin: 2px 40px;
            animation: sunRotate 4s linear infinite;
        }

        .sun-ray:nth-child(2) { transform: translateX(-50%) rotate(45deg); }
        .sun-ray:nth-child(3) { transform: translateX(-50%) rotate(90deg); }
        .sun-ray:nth-child(4) { transform: translateX(-50%) rotate(135deg); }
        .sun-ray:nth-child(5) { transform: translateX(-50%) rotate(180deg); }
        .sun-ray:nth-child(6) { transform: translateX(-50%) rotate(225deg); }
        .sun-ray:nth-child(7) { transform: translateX(-50%) rotate(270deg); }
        .sun-ray:nth-child(8) { transform: translateX(-50%) rotate(315deg); }

        @keyframes sunPulse {
            0% { box-shadow: 0 0 30px #ffeb3b; }
            100% { box-shadow: 0 0 50px #ffeb3b, 0 0 80px #ffeb3b; }
        }

        @keyframes sunRotate {
            0% { transform: translateX(-50%) rotate(0deg); }
            100% { transform: translateX(-50%) rotate(360deg); }
        }

        /* Snow Card */
        .snow-card {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
        }

        .snowflake {
            position: absolute;
            color: white;
            font-size: 20px;
            animation: snowFall 3s linear infinite;
        }

        @keyframes snowFall {
            0% { 
                transform: translateY(-20px) rotate(0deg); 
                opacity: 1; 
            }
            100% { 
                transform: translateY(370px) rotate(360deg); 
                opacity: 0; 
            }
        }

        .title {
            text-align: center;
            margin-bottom: 30px;
            font-size: 32px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
    </style>
</head>
<body>
    <h1 class="title">Animated Weather Cards</h1>
    
    <div class="container">
        <!-- Wind Card -->
        <div class="weather-card wind-card">
            <div class="wind-lines">
                <div class="wind-line"></div>
                <div class="wind-line"></div>
                <div class="wind-line"></div>
                <div class="wind-line"></div>
            </div>
            <div class="card-content">
                <div class="weather-title">Windy</div>
                <div class="weather-temp">18°C • 25 km/h</div>
            </div>
        </div>

        <!-- Rain Card -->
        <div class="weather-card rain-card" id="rainCard">
            <div class="card-content">
                <div class="weather-title">Rainy</div>
                <div class="weather-temp">15°C • Heavy Rain</div>
            </div>
        </div>

        <!-- Sun Card -->
        <div class="weather-card sun-card">
            <div class="sun"></div>
            <div class="sun-ray"></div>
            <div class="sun-ray"></div>
            <div class="sun-ray"></div>
            <div class="sun-ray"></div>
            <div class="sun-ray"></div>
            <div class="sun-ray"></div>
            <div class="sun-ray"></div>
            <div class="sun-ray"></div>
            <div class="card-content">
                <div class="weather-title">Sunny</div>
                <div class="weather-temp">28°C • Clear Sky</div>
            </div>
        </div>

        <!-- Snow Card -->
        <div class="weather-card snow-card" id="snowCard">
            <div class="card-content">
                <div class="weather-title">Snowy</div>
                <div class="weather-temp">-5°C • Light Snow</div>
            </div>
        </div>
    </div>

    <script>
        // Create raindrops
        function createRaindrops() {
            const rainCard = document.getElementById('rainCard');
            for (let i = 0; i < 50; i++) {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = Math.random() * 100 + '%';
                raindrop.style.animationDelay = Math.random() * 1 + 's';
                raindrop.style.animationDuration = (Math.random() * 0.5 + 0.5) + 's';
                rainCard.appendChild(raindrop);
            }
        }

        // Create snowflakes
        function createSnowflakes() {
            const snowCard = document.getElementById('snowCard');
            const snowflakeSymbols = ['❄', '❅', '❆'];
            
            for (let i = 0; i < 30; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.textContent = snowflakeSymbols[Math.floor(Math.random() * snowflakeSymbols.length)];
                snowflake.style.left = Math.random() * 100 + '%';
                snowflake.style.animationDelay = Math.random() * 3 + 's';
                snowflake.style.animationDuration = (Math.random() * 2 + 2) + 's';
                snowflake.style.fontSize = (Math.random() * 10 + 15) + 'px';
                snowCard.appendChild(snowflake);
            }
        }

        // Initialize animations
        document.addEventListener('DOMContentLoaded', function() {
            createRaindrops();
            createSnowflakes();
        });

        // Function to refresh animations (can be called to restart)
        function refreshAnimations() {
            // Clear existing raindrops and snowflakes
            const rainCard = document.getElementById('rainCard');
            const snowCard = document.getElementById('snowCard');
            
            rainCard.querySelectorAll('.raindrop').forEach(drop => drop.remove());
            snowCard.querySelectorAll('.snowflake').forEach(flake => flake.remove());
            
            // Recreate animations
            createRaindrops();
            createSnowflakes();
        }

        // Optional: Refresh animations every 10 seconds for variety
        setInterval(refreshAnimations, 10000);
    </script>
</body>
</html>
